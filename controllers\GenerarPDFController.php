<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../vendor/setasign/fpdf/fpdf.php'; // Ruta corregida para FPDF

class GenerarPDFController {
    private $db;

    public function __construct() {
        try {
            // Corregir la forma de obtener la conexión a la base de datos
            $database = new Database();
            $this->db = $database->getConnection();

            if (!$this->db) {
                throw new Exception('No se pudo conectar a la base de datos');
            }
        } catch (Exception $e) {
            throw new Exception('Error en la conexión a la base de datos: ' . $e->getMessage());
        }
    }

    /**
     * Genera un PDF con los datos del formulario
     */
    public function generarReportePDF($datos) {
        try {
            // Validar que se recibieron datos
            if (empty($datos)) {
                throw new Exception('No se recibieron datos para generar el PDF');
            }

            // Verificar que FPDF esté disponible
            if (!class_exists('FPDF')) {
                throw new Exception('La librería FPDF no está disponible');
            }

            // Crear instancia de FPDF
            $pdf = new FPDF('P', 'mm', 'A4');
            $pdf->AddPage();
        
        // Configurar fuentes
        $pdf->SetFont('Arial', 'B', 16);
        
        // Encabezado
        $pdf->SetFillColor(16, 163, 127); // Color #10a37f
        $pdf->SetTextColor(255, 255, 255);
        $pdf->Cell(0, 15, 'Reporte del Sistema de Alimentación Escolar', 0, 1, 'C', true);
        $pdf->SetTextColor(0, 0, 0);
        $pdf->SetFont('Arial', '', 10);
        $pdf->Cell(0, 8, 'Fecha de generación: ' . date('d/m/Y'), 0, 1, 'C');
        
        // Período
        if (isset($datos['fecha_inicio']) && isset($datos['fecha_fin'])) {
            $pdf->Cell(0, 8, 'Período: ' . $datos['fecha_inicio'] . ' al ' . $datos['fecha_fin'], 0, 1, 'C');
        }
        
        $pdf->Ln(5);
        
        // Información General
        $pdf->SetFont('Arial', 'B', 14);
        $pdf->SetFillColor(240, 240, 240);
        $pdf->Cell(0, 10, 'Información General', 0, 1, 'L', true);
        $pdf->Ln(2);
        
        // Tabla de información
        $pdf->SetFont('Arial', 'B', 10);
        $this->agregarFilaTabla($pdf, 'Parroquia:', isset($datos['parroquia']) ? $datos['parroquia'] : 'No especificado');
        $this->agregarFilaTabla($pdf, 'Responsable:', isset($datos['responsable']) ? $datos['responsable'] : 'No especificado');
        $this->agregarFilaTabla($pdf, 'Institución:', isset($datos['institucion']) ? $datos['institucion'] : 'No especificado');
        $this->agregarFilaTabla($pdf, 'Código DEA:', isset($datos['codigo_dea']) ? $datos['codigo_dea'] : 'No especificado');
        $this->agregarFilaTabla($pdf, 'Nivel:', isset($datos['nivel']) ? $datos['nivel'] : 'No especificado');
        $this->agregarFilaTabla($pdf, 'Menú del día:', isset($datos['menu_dia']) ? $datos['menu_dia'] : 'No especificado');
        
        $pdf->Ln(5);
        
        // Datos de Asistencia
        $pdf->SetFont('Arial', 'B', 14);
        $pdf->SetFillColor(240, 240, 240);
        $pdf->Cell(0, 10, 'Datos de Asistencia', 0, 1, 'L', true);
        $pdf->Ln(2);
        
        // Tabla de asistencia
        $pdf->SetFont('Arial', 'B', 10);
        $this->agregarFilaTabla($pdf, 'Cantidad de Cocineras:', isset($datos['cant_cocineros']) ? $datos['cant_cocineros'] : '0');
        $this->agregarFilaTabla($pdf, 'Cocineras Inasistentes:', isset($datos['cocineros_inasistentes']) ? $datos['cocineros_inasistentes'] : '0');
        $this->agregarFilaTabla($pdf, 'Matrícula Inscrita:', isset($datos['matricula_inscrita']) ? $datos['matricula_inscrita'] : '0');
        $this->agregarFilaTabla($pdf, 'Matrícula Asistente:', isset($datos['matricula_asistente']) ? $datos['matricula_asistente'] : '0');
        $this->agregarFilaTabla($pdf, 'Estudiantes que recibieron la ingesta:', isset($datos['estudiantes_ingesta']) ? $datos['estudiantes_ingesta'] : '0');
        $this->agregarFilaTabla($pdf, 'Estudiantes que repiten:', isset($datos['estudiantes_repiten']) ? $datos['estudiantes_repiten'] : '0');
        $this->agregarFilaTabla($pdf, 'Docentes que recibieron la ingesta:', isset($datos['docentes_reciben']) ? $datos['docentes_reciben'] : '0');
        $this->agregarFilaTabla($pdf, 'Casos Vulnerables:', isset($datos['casos_vulnerables']) ? $datos['casos_vulnerables'] : '0');
        $this->agregarFilaTabla($pdf, 'Total de Comensales:', isset($datos['total_comensales']) ? $datos['total_comensales'] : '0');
        
        $pdf->Ln(5);
        
        // Incidencias y Observaciones
        $pdf->SetFont('Arial', 'B', 14);
        $pdf->SetFillColor(240, 240, 240);
        $pdf->Cell(0, 10, 'Incidencias y Observaciones', 0, 1, 'L', true);
        $pdf->Ln(2);
        
        // Tabla de incidencias
        $pdf->SetFont('Arial', 'B', 10);
        $this->agregarFilaTabla($pdf, 'Incidencias:', isset($datos['incidencias']) ? $this->formatearIncidencia($datos['incidencias']) : 'Ninguna');
        $this->agregarFilaTabla($pdf, 'Observaciones:', isset($datos['observaciones']) ? $datos['observaciones'] : 'Sin observaciones');
        
            // Generar el PDF
            $pdf->Output('D', 'reporte_comedor_escolar.pdf');

        } catch (Exception $e) {
            throw new Exception('Error al generar el PDF: ' . $e->getMessage());
        }
    }
    
    /**
     * Agrega una fila a la tabla del PDF
     */
    private function agregarFilaTabla($pdf, $etiqueta, $valor) {
        $pdf->SetFont('Arial', 'B', 10);
        $pdf->Cell(80, 8, $etiqueta, 1);
        $pdf->SetFont('Arial', '', 10);
        $pdf->Cell(110, 8, $valor, 1);
        $pdf->Ln();
    }

    /**
     * Formatea el texto de incidencia para el PDF
     */
    private function formatearIncidencia($incidencia) {
        switch ($incidencia) {
            case 'ninguna':
                return 'Ninguna';
            case 'falta_insumos':
                return 'Falta de insumos';
            case 'falla_equipos':
                return 'Falla de equipos';
            case 'otros':
                return 'Otros';
            default:
                return $incidencia;
        }
    }
}
?>


