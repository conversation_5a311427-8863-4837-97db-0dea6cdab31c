<?php
// Archivo de diagnóstico para verificar el sistema
echo "<h1>Diagnóstico del Sistema de Reportes</h1>";

// 1. Verificar PHP
echo "<h2>1. Información de PHP</h2>";
echo "<p><strong>Versión de PHP:</strong> " . phpversion() . "</p>";
echo "<p><strong>Extensiones cargadas:</strong></p>";
$extensions = ['pdo', 'pdo_mysql', 'mbstring'];
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? '✅ Cargada' : '❌ No cargada';
    echo "<p>- $ext: $status</p>";
}

// 2. Verificar archivos
echo "<h2>2. Verificación de Archivos</h2>";
$archivos = [
    'config/database.php',
    'controllers/GenerarPDFController.php',
    'api/generar_pdf.php',
    'vendor/setasign/fpdf/fpdf.php',
    'views/reportes.php',
    'assets/js/reportes.js'
];

foreach ($archivos as $archivo) {
    $existe = file_exists(__DIR__ . '/' . $archivo);
    $status = $existe ? '✅ Existe' : '❌ No existe';
    echo "<p>- $archivo: $status</p>";
}

// 3. Verificar conexión a base de datos
echo "<h2>3. Conexión a Base de Datos</h2>";
try {
    require_once __DIR__ . '/config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p>✅ Conexión exitosa a la base de datos</p>";
        
        // Verificar algunas tablas
        $tablas = ['usuarios', 'asistencia', 'menu', 'producto'];
        foreach ($tablas as $tabla) {
            try {
                $stmt = $db->query("SELECT COUNT(*) FROM $tabla");
                $count = $stmt->fetchColumn();
                echo "<p>- Tabla '$tabla': ✅ $count registros</p>";
            } catch (Exception $e) {
                echo "<p>- Tabla '$tabla': ❌ Error: " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p>❌ No se pudo conectar a la base de datos</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error de conexión: " . $e->getMessage() . "</p>";
}

// 4. Verificar FPDF
echo "<h2>4. Verificación de FPDF</h2>";
try {
    require_once __DIR__ . '/vendor/setasign/fpdf/fpdf.php';
    if (class_exists('FPDF')) {
        echo "<p>✅ FPDF está disponible</p>";
        
        // Probar crear una instancia
        $pdf = new FPDF();
        echo "<p>✅ Se puede crear una instancia de FPDF</p>";
    } else {
        echo "<p>❌ FPDF no está disponible</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error con FPDF: " . $e->getMessage() . "</p>";
}

// 5. Verificar controlador de reportes
echo "<h2>5. Verificación del Controlador de Reportes</h2>";
try {
    require_once __DIR__ . '/controllers/ReporteController.php';
    $reporteController = new ReporteController();
    echo "<p>✅ ReporteController se puede instanciar</p>";
    
    // Probar algunos métodos
    $fecha_inicio = date('Y-m-01');
    $fecha_fin = date('Y-m-t');
    
    try {
        $resumen = $reporteController->getResumenAsistencia($fecha_inicio, $fecha_fin);
        echo "<p>✅ getResumenAsistencia funciona</p>";
    } catch (Exception $e) {
        echo "<p>❌ Error en getResumenAsistencia: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error con ReporteController: " . $e->getMessage() . "</p>";
}

// 6. Verificar permisos de escritura
echo "<h2>6. Verificación de Permisos</h2>";
$directorios = [__DIR__, __DIR__ . '/assets', __DIR__ . '/views'];
foreach ($directorios as $dir) {
    $escribible = is_writable($dir);
    $status = $escribible ? '✅ Escribible' : '❌ No escribible';
    echo "<p>- $dir: $status</p>";
}

echo "<h2>Diagnóstico Completado</h2>";
echo "<p>Si todos los elementos muestran ✅, el sistema debería funcionar correctamente.</p>";
echo "<p><a href='views/reportes.php'>Ir a Reportes</a> | <a href='test_pdf.php'>Probar PDF</a></p>";
?>
