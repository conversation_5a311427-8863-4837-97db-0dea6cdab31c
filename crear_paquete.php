<?php
/**
 * Script para crear el paquete de actualización de reportes
 * Genera un ZIP con todos los archivos necesarios
 */

echo "<h1>🚀 Creador de Paquete de Actualización - Sistema de Reportes</h1>";

// Verificar que la extensión ZIP esté disponible
if (!extension_loaded('zip')) {
    die("❌ Error: La extensión ZIP no está disponible en PHP.");
}

// Nombre del archivo ZIP
$zipFileName = 'actualizacion_reportes_' . date('Y-m-d_H-i-s') . '.zip';
$zip = new ZipArchive();

if ($zip->open($zipFileName, ZipArchive::CREATE) !== TRUE) {
    die("❌ Error: No se pudo crear el archivo ZIP.");
}

echo "<h2>📦 Creando paquete: <strong>$zipFileName</strong></h2>";

// Lista de archivos obligatorios a incluir
$archivosObligatorios = [
    // Archivos modificados principales
    'views/reportes.php' => 'views/reportes.php',
    'assets/js/reportes.js' => 'assets/js/reportes.js',
    'controllers/GenerarPDFController.php' => 'controllers/GenerarPDFController.php',
    'api/generar_pdf.php' => 'api/generar_pdf.php',
    'controllers/AuthController.php' => 'controllers/AuthController.php',
    
    // Configuración
    'composer.json' => 'composer.json',
    
    // Instrucciones
    'INSTRUCCIONES_INSTALACION.md' => 'INSTRUCCIONES_INSTALACION.md'
];

// Lista de archivos de prueba (opcionales)
$archivosPrueba = [
    'test_pdf.php' => 'pruebas/test_pdf.php',
    'test_form.php' => 'pruebas/test_form.php',
    'test_usuario.php' => 'pruebas/test_usuario.php',
    'test_seguridad.php' => 'pruebas/test_seguridad.php',
    'diagnostico.php' => 'pruebas/diagnostico.php'
];

echo "<h3>✅ Agregando archivos obligatorios:</h3>";
echo "<ul>";

// Agregar archivos obligatorios
foreach ($archivosObligatorios as $origen => $destino) {
    if (file_exists($origen)) {
        $zip->addFile($origen, $destino);
        echo "<li>✅ $origen → $destino</li>";
    } else {
        echo "<li>❌ <strong>FALTA:</strong> $origen</li>";
    }
}

echo "</ul>";

echo "<h3>📋 Agregando archivos de prueba:</h3>";
echo "<ul>";

// Agregar archivos de prueba
foreach ($archivosPrueba as $origen => $destino) {
    if (file_exists($origen)) {
        $zip->addFile($origen, $destino);
        echo "<li>✅ $origen → $destino</li>";
    } else {
        echo "<li>⚠️ <strong>OPCIONAL:</strong> $origen (no encontrado)</li>";
    }
}

echo "</ul>";

echo "<h3>📚 Agregando librería FPDF:</h3>";

// Función para agregar directorio recursivamente
function agregarDirectorio($zip, $dir, $zipDir = '') {
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir),
        RecursiveIteratorIterator::LEAVES_ONLY
    );

    foreach ($files as $name => $file) {
        if (!$file->isDir()) {
            $filePath = $file->getRealPath();
            $relativePath = $zipDir . substr($filePath, strlen($dir) + 1);
            
            // Normalizar separadores de directorio para ZIP
            $relativePath = str_replace('\\', '/', $relativePath);
            
            $zip->addFile($filePath, $relativePath);
        }
    }
}

// Agregar vendor/setasign/fpdf
if (is_dir('vendor/setasign/fpdf')) {
    agregarDirectorio($zip, 'vendor/setasign/fpdf', 'vendor/setasign/fpdf/');
    echo "<ul><li>✅ vendor/setasign/fpdf/ (librería FPDF completa)</li></ul>";
} else {
    echo "<ul><li>❌ <strong>FALTA:</strong> vendor/setasign/fpdf/</li></ul>";
}

// Agregar archivos de composer necesarios
$archivosComposer = [
    'vendor/autoload.php' => 'vendor/autoload.php',
    'vendor/composer/autoload_real.php' => 'vendor/composer/autoload_real.php',
    'vendor/composer/autoload_static.php' => 'vendor/composer/autoload_static.php',
    'vendor/composer/autoload_classmap.php' => 'vendor/composer/autoload_classmap.php',
    'vendor/composer/autoload_psr4.php' => 'vendor/composer/autoload_psr4.php',
    'vendor/composer/ClassLoader.php' => 'vendor/composer/ClassLoader.php',
    'vendor/composer/installed.json' => 'vendor/composer/installed.json'
];

echo "<h3>🔧 Agregando archivos de Composer:</h3>";
echo "<ul>";

foreach ($archivosComposer as $origen => $destino) {
    if (file_exists($origen)) {
        $zip->addFile($origen, $destino);
        echo "<li>✅ $destino</li>";
    } else {
        echo "<li>⚠️ $destino (no encontrado)</li>";
    }
}

echo "</ul>";

// Crear archivo README dentro del ZIP
$readmeContent = "# PAQUETE DE ACTUALIZACIÓN - SISTEMA DE REPORTES

## 📁 Contenido del Paquete:

### Archivos Principales:
- views/reportes.php
- assets/js/reportes.js  
- controllers/GenerarPDFController.php
- api/generar_pdf.php
- controllers/AuthController.php
- composer.json

### Librería FPDF:
- vendor/setasign/fpdf/ (completa)
- vendor/autoload.php y archivos de Composer

### Herramientas de Prueba:
- pruebas/test_*.php
- pruebas/diagnostico.php

## 🚀 Instalación:
1. Lee INSTRUCCIONES_INSTALACION.md
2. Haz backup de tus archivos actuales
3. Copia los archivos a tu proyecto
4. Ejecuta 'composer install' o usa la carpeta vendor incluida
5. Prueba con diagnostico.php

## ⚠️ Importante:
- Haz backup antes de instalar
- Debes estar logueado para usar reportes
- El campo responsable ahora es automático

¡Disfruta del sistema mejorado!
";

$zip->addFromString('README.txt', $readmeContent);

// Cerrar el ZIP
$result = $zip->close();

if ($result === TRUE) {
    $fileSize = round(filesize($zipFileName) / 1024 / 1024, 2);
    echo "<h2>🎉 ¡Paquete creado exitosamente!</h2>";
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>📦 Información del Paquete:</h3>";
    echo "<p><strong>Archivo:</strong> $zipFileName</p>";
    echo "<p><strong>Tamaño:</strong> {$fileSize} MB</p>";
    echo "<p><strong>Archivos incluidos:</strong> " . $zip->numFiles . "</p>";
    echo "<p><strong>Ubicación:</strong> " . realpath($zipFileName) . "</p>";
    echo "</div>";
    
    echo "<h3>📋 Próximos pasos:</h3>";
    echo "<ol>";
    echo "<li>Descarga el archivo: <a href='$zipFileName' download><strong>$zipFileName</strong></a></li>";
    echo "<li>Envía el ZIP a tu compañero</li>";
    echo "<li>Dile que lea <strong>INSTRUCCIONES_INSTALACION.md</strong></li>";
    echo "<li>Que ejecute <strong>diagnostico.php</strong> después de instalar</li>";
    echo "</ol>";
    
} else {
    echo "<h2>❌ Error al crear el paquete</h2>";
    echo "<p>Código de error: $result</p>";
}

echo "<hr>";
echo "<p><em>Script ejecutado el " . date('Y-m-d H:i:s') . "</em></p>";
?>
