:root {
    --primary-color: #10a37f;
    --secondary-color: #202123;
    --background-color: #f7f7f8;
    --text-color: #353740;
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 60px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Söhne', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    font-size: 16px;
}

.sidebar {
    position: fixed;
    width: var(--sidebar-width);
    height: 100vh;
    background-color: var(--secondary-color);
    color: white;
    padding: 1rem;
    transition: width 0.3s ease;
    z-index: 1000;
    border-radius: 0 12px 12px 0;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.5rem;
}

.sidebar-menu {
    list-style: none;
    margin-top: 1rem;
}

.sidebar-menu li {
    margin: 0.5rem 0;
}

.sidebar-menu a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    transition: background-color 0.3s ease;
}

.sidebar-menu a:hover {
    background-color: rgba(255,255,255,0.15);
}

.sidebar-menu .menu-text {
    margin-left: 0.75rem;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .menu-text {
    opacity: 0;
    display: none;
}

.main-content {
    margin-left: var(--sidebar-width);
    padding: 2rem;
    transition: margin-left 0.3s ease;
}

.main-content.expanded {
    margin-left: var(--sidebar-collapsed-width);
}

.card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(16, 163, 127, 0.15);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 12px 24px rgba(16, 163, 127, 0.25);
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #ccc;
    border-radius: 12px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 8px rgba(16, 163, 127, 0.4);
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 8px rgba(16, 163, 127, 0.15);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #0d8a6c;
    box-shadow: 0 6px 12px rgba(16, 163, 127, 0.3);
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 0.75rem;
    margin: 1.5rem 0;
}

.table th,
.table td {
    padding: 1rem 1.25rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    text-align: left;
    vertical-align: middle;
    box-shadow: 0 4px 8px rgba(16, 163, 127, 0.1);
}

.table th {
    font-weight: 700;
    color: var(--secondary-color);
}

.alert {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.75);
    z-index: 1100;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease forwards;
}

.modal-content {
    background-color: rgba(255, 255, 255, 0.95);
    margin: 10% auto;
    padding: 30px 40px;
    border-radius: 12px;
    width: 90%;
    max-width: 450px;
    position: relative;
    box-shadow: 0 12px 24px rgba(16, 163, 127, 0.25);
    text-align: center;
    animation: slideDown 0.3s ease forwards;
}

.warning-modal-content {
    border: 4px solid #d93025;
}

.warning-icon {
    font-size: 60px;
    color: #d93025;
    margin-bottom: 20px;
}

.modal-icon {
    font-size: 60px;
    margin-bottom: 20px;
}

.success-icon {
    color: #28a745; /* Bootstrap success green */
}

.success-modal-content {
    border: 4px solid #28a745;
}

.success-title {
    font-size: 28px;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 15px;
}

.success-message {
    font-size: 16px;
    color: #333;
    margin-bottom: 25px;
}

.success-buttons {
    display: flex;
    justify-content: center;
}

.success-buttons .btn {
    min-width: 100px;
    font-weight: 600;
    font-size: 16px;
    padding: 10px 20px;
    border-radius: 12px;
}

.friendly-modal-content {
    border: 4px solid #17b3b8; /* Bootstrap info color */
}

.friendly-icon {
    font-size: 60px;
    color: #1798b8;
    margin-bottom: 20px;
}

.friendly-title {
    font-size: 28px;
    font-weight: 700;
    color: #17a2b8;
    margin-bottom: 15px;
}

.friendly-message {
    font-size: 16px;
    color: #333;
    margin-bottom: 25px;
}

.friendly-buttons {
    display: flex;
    justify-content: center;
}

.friendly-buttons .btn {
    min-width: 100px;
    font-weight: 600;
    font-size: 16px;
    padding: 10px 20px;
    border-radius: 12px;
}

.warning-title {
    font-size: 28px;
    font-weight: 700;
    color: #d93025;
    margin-bottom: 15px;
}

.warning-message {
    font-size: 16px;
    color: #333;
    margin-bottom: 25px;
}

.warning-buttons {
    display: flex;
    justify-content: center;
}

.warning-buttons .btn {
    min-width: 100px;
    font-weight: 600;
    font-size: 16px;
    padding: 10px 20px;
    border-radius: 12px;
}

@keyframes fadeIn {
    from {opacity: 0;}
    to {opacity: 1;}
}

@keyframes slideDown {
    from {transform: translateY(-20px); opacity: 0;}
    to {transform: translateY(0); opacity: 1;}
}
