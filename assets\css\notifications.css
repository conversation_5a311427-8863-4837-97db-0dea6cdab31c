    /* Notification styles */

.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    background-color: #ffa5a5;
    border: 1px solid #000000;
    color: #000000;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    max-width: 320px;
    z-index: 1000;
    font-weight: 600;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.notification h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    width: 100%;
}

.notification ul {
    margin: 0;
    padding-left: 1.2rem;
    max-height: 150px;
    overflow-y: auto;
    font-weight: normal;
    font-size: 0.9rem;
    color: #000000;
    width: 100%;
}

.notification ul li {
    margin-bottom: 0.3rem;
}

.notification button.close-btn {
    position: absolute;
    top: 0.3rem;
    right: 0.5rem;
    background: transparent;
    border: none;
    font-size: 1.2rem;
    font-weight: bold;
    color: #856404;
    cursor: pointer;
    line-height: 1;
    padding: 0;
    user-select: none;
    transition: color 0.3s ease;
}

.notification button.close-btn:hover {
    color: #533f03;
}
