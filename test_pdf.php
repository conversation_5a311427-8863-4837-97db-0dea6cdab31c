<?php
// Archivo de prueba para verificar la generación de PDF
require_once __DIR__ . '/controllers/GenerarPDFController.php';

// Datos de prueba
$datosPrueba = [
    'parroquia' => 'Parroquia de Prueba',
    'responsable' => '<PERSON>',
    'institucion' => 'Escuela de Prueba',
    'codigo_dea' => '12345',
    'nivel' => 'Primaria',
    'menu_dia' => 'Arroz con pollo',
    'cant_cocineros' => '3',
    'cocineros_inasistentes' => '0',
    'matricula_inscrita' => '100',
    'matricula_asistente' => '95',
    'estudiantes_ingesta' => '95',
    'estudiantes_repiten' => '5',
    'docentes_reciben' => '10',
    'casos_vulnerables' => '2',
    'total_comensales' => '112',
    'incidencias' => 'ninguna',
    'observaciones' => 'Todo funcionó correctamente',
    'fecha_inicio' => '2024-01-01',
    'fecha_fin' => '2024-01-31'
];

try {
    echo "<h2>Probando generación de PDF...</h2>";
    echo "<p>Datos de prueba:</p>";
    echo "<pre>" . print_r($datosPrueba, true) . "</pre>";
    
    // Crear instancia del controlador
    $pdfController = new GenerarPDFController();
    
    // Generar el PDF
    echo "<p>Generando PDF...</p>";
    $pdfController->generarReportePDF($datosPrueba);
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; border: 1px solid red; margin: 20px;'>";
    echo "<h3>Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
    echo "<h4>Stack trace:</h4>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?>
