/**
 * Sistema de Reportes - JavaScript
 * Maneja la funcionalidad de reportes, gráficos y exportación
 */

// Variables globales para los datos
let reportesData = {
    asistencia: window.datosAsistencia || [],
    consumo: window.datosConsumo || []
};

// Configuración de gráficos
const chartConfig = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
        }
    }
};

/**
 * Inicialización del sistema de reportes
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    initializeCharts();
    initializeFormHandlers();
    initializeExportButtons();
});

/**
 * Inicializar las pestañas de reportes
 */
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const reportSections = document.querySelectorAll('.report-section');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remover clase active de todos los botones y secciones
            tabButtons.forEach(btn => btn.classList.remove('active'));
            reportSections.forEach(section => section.classList.remove('active'));

            // Agregar clase active al botón y sección correspondiente
            this.classList.add('active');
            const targetSection = document.getElementById(targetTab);
            if (targetSection) {
                targetSection.classList.add('active');
            }
        });
    });
}
/**
 * Inicializar los gráficos de reportes
 */
function initializeCharts() {
    // Gráfico de resumen general
    const resumenCtx = document.getElementById('resumenChart');
    if (resumenCtx) {
        new Chart(resumenCtx, {
            type: 'doughnut',
            data: {
                labels: ['Estudiantes', 'Docentes'],
                datasets: [{
                    data: [
                        document.querySelector('.stat-card .stat-value')?.textContent || 0,
                        document.querySelectorAll('.stat-card .stat-value')[1]?.textContent || 0
                    ],
                    backgroundColor: ['#10a37f', '#16a085']
                }]
            },
            options: chartConfig
        });
    }

    // Gráfico de asistencia por día
    const asistenciaCtx = document.getElementById('asistenciaChart');
    if (asistenciaCtx && reportesData.asistencia.length > 0) {
        const fechas = reportesData.asistencia.map(item => item.fecha);
        const totales = reportesData.asistencia.map(item => item.total);

        new Chart(asistenciaCtx, {
            type: 'line',
            data: {
                labels: fechas,
                datasets: [{
                    label: 'Asistencia Total',
                    data: totales,
                    borderColor: '#10a37f',
                    backgroundColor: 'rgba(16, 163, 127, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                ...chartConfig,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Gráfico de consumo de insumos
    const consumoCtx = document.getElementById('consumoChart');
    if (consumoCtx && reportesData.consumo.length > 0) {
        const productos = reportesData.consumo.map(item => item.producto);
        const cantidades = reportesData.consumo.map(item => item.total);

        new Chart(consumoCtx, {
            type: 'bar',
            data: {
                labels: productos,
                datasets: [{
                    label: 'Cantidad Consumida',
                    data: cantidades,
                    backgroundColor: '#10a37f'
                }]
            },
            options: {
                ...chartConfig,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

/**
 * Inicializar manejadores de formularios
 */
function initializeFormHandlers() {
    // Calcular total de comensales automáticamente
    const calcularTotalComensales = () => {
        const estudiantesIngesta = parseInt(document.getElementById('estudiantes_ingesta')?.value || 0);
        const estudiantesRepiten = parseInt(document.getElementById('estudiantes_repiten')?.value || 0);
        const docentesReciben = parseInt(document.getElementById('docentes_reciben')?.value || 0);
        const casosVulnerables = parseInt(document.getElementById('casos_vulnerables')?.value || 0);

        const total = estudiantesIngesta + estudiantesRepiten + docentesReciben + casosVulnerables;
        const totalComensalesField = document.getElementById('total_comensales');
        if (totalComensalesField) {
            totalComensalesField.value = total;
        }
    };

    // Agregar event listeners para cálculo automático
    ['estudiantes_ingesta', 'estudiantes_repiten', 'docentes_reciben', 'casos_vulnerables'].forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', calcularTotalComensales);
        }
    });

    // Validación del formulario
    const reportForm = document.getElementById('reportForm');
    if (reportForm) {
        reportForm.addEventListener('submit', function(e) {
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
                alert('Por favor, complete todos los campos requeridos.');
            }
            this.classList.add('was-validated');
        });
    }
}

/**
 * Inicializar botones de exportación
 */
function initializeExportButtons() {
    // Botón de exportar a Excel
    const exportExcelBtn = document.getElementById('exportExcelBtn');
    if (exportExcelBtn) {
        exportExcelBtn.addEventListener('click', exportToExcel);
    }
}

/**
 * Exportar datos a Excel
 */
function exportToExcel() {
    // Esta función se puede implementar usando una librería como SheetJS
    alert('Funcionalidad de exportación a Excel en desarrollo');
}