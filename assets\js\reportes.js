/**
 * Función para exportar el formulario de reporte a PDF
 */
function exportToPDF() {
    console.log('Iniciando exportación a PDF');
    
    // Verificar si la biblioteca html2pdf está disponible
    if (typeof html2pdf !== 'function') {
        console.error('Error: La biblioteca html2pdf no está cargada');
        alert('Error: No se puede generar el PDF. La biblioteca necesaria no está disponible.');
        return;
    }
    
    // En lugar de capturar solo el formulario, vamos a capturar toda la sección activa
    const activeSection = document.querySelector('.report-section.active');
    if (!activeSection) {
        console.error('Error: No se encontró ninguna sección activa');
        alert('Error: No se puede generar el PDF. No se encontró la sección activa.');
        return;
    }
    
    // Crear una copia del contenido para no modificar el original
    const contentClone = activeSection.cloneNode(true);
    
    // Si la sección activa es el formulario, vamos a formatear mejor los datos
    if (activeSection.id === 'formulario') {
        // Obtener el formulario dentro de la sección
        const form = contentClone.querySelector('#reportForm');
        if (form) {
            // Crear un contenedor para el reporte formateado
            const reportContainer = document.createElement('div');
            reportContainer.className = 'pdf-report-container';
            reportContainer.style.fontFamily = 'Arial, sans-serif';
            reportContainer.style.maxWidth = '800px';
            reportContainer.style.margin = '0 auto';
            reportContainer.style.padding = '20px';
            
            // Obtener datos del formulario
            const formData = {};
            form.querySelectorAll('input, select, textarea').forEach(element => {
                if (element.name) {
                    formData[element.name] = element.value || '';
                    console.log(`Campo ${element.name}: ${element.value}`);
                }
            });
            
            // Obtener fechas del período
            const fechaInicio = document.getElementById('fecha_inicio')?.value || '';
            const fechaFin = document.getElementById('fecha_fin')?.value || '';
            formData.periodo = `${fechaInicio} al ${fechaFin}`;
            
            // Crear encabezado del reporte
            const header = document.createElement('div');
            header.style.textAlign = 'center';
            header.style.marginBottom = '30px';
            header.style.borderBottom = '2px solid #10a37f';
            header.style.paddingBottom = '10px';
            
            header.innerHTML = `
                <h1 style="color: #10a37f; margin-bottom: 5px;">Reporte del Sistema de Alimentación Escolar</h1>
                <p>Fecha de generación: ${new Date().toLocaleDateString('es-ES')}</p>
                <p>Período: ${formData.periodo}</p>
            `;
            
            reportContainer.appendChild(header);
            
            // Crear sección de información general
            const infoSection = document.createElement('div');
            infoSection.style.marginBottom = '20px';
            
            infoSection.innerHTML = `
                <h2 style="color: #10a37f; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-bottom: 15px;">Información General</h2>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Parroquia:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.parroquia || 'No especificado'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Responsable:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.responsable || 'No especificado'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Institución:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.institucion || 'No especificado'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Código DEA:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.codigo_dea || 'No especificado'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Nivel:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.nivel || 'No especificado'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Menú del día:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.menu_dia || 'No especificado'}</td>
                    </tr>
                </table>
            `;
            
            reportContainer.appendChild(infoSection);
            
            // Crear sección de asistencia
            const asistenciaSection = document.createElement('div');
            asistenciaSection.style.marginBottom = '20px';
            
            asistenciaSection.innerHTML = `
                <h2 style="color: #10a37f; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-bottom: 15px;">Datos de Asistencia</h2>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Cantidad de Cocineras:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.cant_cocineros || '0'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Cocineras Inasistentes:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.cocineros_inasistentes || '0'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Matrícula Inscrita:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.matricula_inscrita || '0'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Matrícula Asistente:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.matricula_asistente || '0'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Estudiantes que recibieron ingesta:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.estudiantes_ingesta || '0'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Estudiantes que repiten:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.estudiantes_repiten || '0'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Docentes que reciben:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.docentes_reciben || '0'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Casos Vulnerables:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.casos_vulnerables || '0'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Total de Comensales:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.total_comensales || '0'}</td>
                    </tr>
                </table>
            `;
            
            reportContainer.appendChild(asistenciaSection);
            
            // Crear sección de incidencias y observaciones
            const observacionesSection = document.createElement('div');
            observacionesSection.style.marginBottom = '20px';
            
            observacionesSection.innerHTML = `
                <h2 style="color: #10a37f; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-bottom: 15px;">Incidencias y Observaciones</h2>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Incidencias:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.incidencias || 'Ninguna'}</td>
                    </tr>
                    <tr>
                        <th style="width: 40%; padding: 8px; text-align: left; border-bottom: 1px solid #ddd; font-weight: bold; color: #333;">Observaciones:</th>
                        <td style="padding: 8px; text-align: left; border-bottom: 1px solid #ddd;">${formData.observaciones || 'Sin observaciones'}</td>
                    </tr>
                </table>
            `;
            
            reportContainer.appendChild(observacionesSection);
            
            // Reemplazar el contenido del clon con el reporte formateado
            contentClone.innerHTML = '';
            contentClone.appendChild(reportContainer);
        }
    } else {
        // Para otras secciones, simplemente eliminamos los botones y controles que no queremos en el PDF
        contentClone.querySelectorAll('button, .controls, .actions, input[type="button"]').forEach(el => {
            el.remove();
        });
    }
    
    // Ocultar el elemento pero mantenerlo en el DOM para html2pdf
    contentClone.style.position = 'absolute';
    contentClone.style.left = '-9999px';
    contentClone.style.width = '800px'; // Ancho fijo para el PDF
    document.body.appendChild(contentClone);
    
    // Configuración para html2pdf
    const options = {
        margin: 10,
        filename: 'reporte_comedor_escolar.pdf',
        image: { type: 'jpeg', quality: 1 },
        html2canvas: { scale: 2, useCORS: true, logging: true },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    };
    
    // Mostrar indicador de carga
    const loadingIndicator = document.createElement('div');
    loadingIndicator.innerHTML = '<div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); display: flex; justify-content: center; align-items: center; z-index: 9999;"><div style="background: white; padding: 20px; border-radius: 5px; text-align: center;"><p>Generando PDF...</p></div></div>';
    document.body.appendChild(loadingIndicator);
    
    // Generar el PDF
    html2pdf()
        .from(contentClone)
        .set(options)
        .save()
        .then(() => {
            // Limpiar después de generar el PDF
            document.body.removeChild(contentClone);
            document.body.removeChild(loadingIndicator);
            console.log('PDF generado correctamente');
        })
        .catch(error => {
            console.error('Error al generar el PDF:', error);
            document.body.removeChild(contentClone);
            document.body.removeChild(loadingIndicator);
            alert('Error al generar el PDF: ' + error.message);
        });
}