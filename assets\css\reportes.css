/* 
.report-container {
    padding: 20px;
}
.report-header {
    margin-bottom: 20px;
}
.report-nav {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
}
.report-nav button {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 16px;
    color: #666;
}
.report-nav button.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}
.report-section {
    display: none;
    margin-top: 20px;
}
.report-section.active {
    display: block;
}
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}
.stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}
.stat-card h3 {
    color: #666;
    margin-bottom: 10px;
}
.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}
.chart-container {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}
.form-section {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

*/

/* Estilos para el PDF */
.pdf-container {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    display: none;
}

.pdf-header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #10a37f;
    padding-bottom: 10px;
}

.pdf-header h1 {
    color: #10a37f;
    margin-bottom: 5px;
}

.pdf-section {
    margin-bottom: 20px;
}

.pdf-section h2 {
    color: #10a37f;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.pdf-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.pdf-table th, .pdf-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.pdf-table th {
    width: 40%;
    font-weight: bold;
    color: #333;
}

/* Estilo para secciones temporalmente visibles durante la exportación a PDF */
.report-section.temp-visible {
    display: block;
    page-break-after: always;
    margin-bottom: 20px;
}

/* Asegurarse que cada sección comience en una nueva página en el PDF */
@media print {
    .report-section {
        page-break-after: always;
    }
}

/* Estilos para las pestañas de reportes */
.report-section {
    display: none;
}

.report-section.active {
    display: block;
}

.report-nav {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
}

.tab-btn {
    padding: 10px 15px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: #10a37f;
}

.tab-btn.active {
    color: #10a37f;
    border-bottom-color: #10a37f;
}

/* Estilos para los gráficos */
.chart-container {
    margin-bottom: 20px;
    height: 300px;
}

/* Estilos para las tarjetas de estadísticas */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: #fff;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card h3 {
    color: #666;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 20px;
    font-weight: bold;
    color: #10a37f;
}
