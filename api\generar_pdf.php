<?php
// Habilitar reporte de errores para debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../controllers/GenerarPDFController.php';

// Verificar si es una solicitud POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Verificar que se recibieron datos
        if (empty($_POST)) {
            throw new Exception('No se recibieron datos del formulario');
        }

        // Obtener los datos del formulario
        $datos = $_POST;

        // Debug: mostrar datos recibidos (comentar en producción)
        // echo '<pre>'; print_r($datos); echo '</pre>'; exit;

        // Validar solo los campos más básicos
        $camposRequeridos = ['parroquia', 'responsable', 'institucion'];
        foreach ($camposRequeridos as $campo) {
            if (empty($datos[$campo])) {
                throw new Exception("El campo '$campo' es requerido");
            }
        }

        // Crear instancia del controlador
        $pdfController = new GenerarPDFController();

        // Generar el PDF
        $pdfController->generarReportePDF($datos);

        // No es necesario devolver nada ya que el PDF se descargará directamente
        exit;
    } catch (Exception $e) {
        // Mostrar error detallado
        echo '<!DOCTYPE html>';
        echo '<html lang="es">';
        echo '<head>';
        echo '<meta charset="UTF-8">';
        echo '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
        echo '<title>Error - Generación de PDF</title>';
        echo '<style>';
        echo 'body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }';
        echo '.error-container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto; }';
        echo '.error-title { color: #d32f2f; margin-bottom: 20px; }';
        echo '.error-message { color: #666; margin-bottom: 20px; line-height: 1.6; }';
        echo '.btn { background: #10a37f; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }';
        echo '.btn:hover { background: #0d8f6b; }';
        echo '</style>';
        echo '</head>';
        echo '<body>';
        echo '<div class="error-container">';
        echo '<h2 class="error-title">Error al generar el PDF</h2>';
        echo '<p class="error-message">' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p class="error-message">Archivo: ' . htmlspecialchars($e->getFile()) . '</p>';
        echo '<p class="error-message">Línea: ' . $e->getLine() . '</p>';
        echo '<a href="javascript:history.back()" class="btn">Volver al formulario</a>';
        echo '</div>';
        echo '</body>';
        echo '</html>';
        exit;
    }
} else {
    // Redirigir si no es POST
    header('Location: ../views/reportes.php');
    exit;
}