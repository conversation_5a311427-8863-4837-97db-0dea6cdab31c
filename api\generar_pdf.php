<?php
require_once __DIR__ . '/../controllers/GenerarPDFController.php';

// Verificar si es una solicitud POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Obtener los datos del formulario
        $datos = $_POST;
        
        // Crear instancia del controlador
        $pdfController = new GenerarPDFController();
        
        // Generar el PDF
        $pdfController->generarReportePDF($datos);
        
        // No es necesario devolver nada ya que el PDF se descargará directamente
        exit;
    } catch (Exception $e) {
        // Mostrar error
        echo '<div style="color: red; padding: 20px; text-align: center;">';
        echo '<h2>Error al generar el PDF</h2>';
        echo '<p>' . $e->getMessage() . '</p>';
        echo '<p><a href="javascript:history.back()">Volver</a></p>';
        echo '</div>';
        exit;
    }
} else {
    // Redirigir si no es POST
    header('Location: ../views/reportes.php');
    exit;
}
?>

