<?php
require_once __DIR__ . '/controllers/AuthController.php';

$auth = new AuthController();
$usuarioLogueado = $auth->getCurrentUserName();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Formulario PDF</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #10a37f; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0d8f6b; }
        .container { max-width: 600px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Prueba Simple de Formulario PDF</h1>
        <p>Este es un formulario simplificado para probar la generación de PDF.</p>
        
        <form method="post" action="api/generar_pdf.php" target="_blank">
            <div class="form-group">
                <label for="parroquia">Parroquia *</label>
                <input type="text" id="parroquia" name="parroquia" value="Parroquia de Prueba" required>
            </div>
            
            <div class="form-group">
                <label for="responsable">Responsable *</label>
                <input type="text" id="responsable" name="responsable" value="<?php echo htmlspecialchars($usuarioLogueado ?: 'Usuario de Prueba'); ?>" required>
            </div>
            
            <div class="form-group">
                <label for="institucion">Institución *</label>
                <input type="text" id="institucion" name="institucion" value="Escuela de Prueba" required>
            </div>
            
            <div class="form-group">
                <label for="codigo_dea">Código DEA</label>
                <input type="text" id="codigo_dea" name="codigo_dea" value="12345">
            </div>
            
            <div class="form-group">
                <label for="nivel">Nivel</label>
                <select id="nivel" name="nivel">
                    <option value="">Seleccionar...</option>
                    <option value="Inicial">Inicial</option>
                    <option value="Primaria" selected>Primaria</option>
                    <option value="Secundaria">Secundaria</option>
                    <option value="Mixto">Mixto</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="menu_dia">Menú del día</label>
                <input type="text" id="menu_dia" name="menu_dia" value="Arroz con pollo">
            </div>
            
            <div class="form-group">
                <label for="cant_cocineros">Cantidad de cocineras</label>
                <input type="number" id="cant_cocineros" name="cant_cocineros" value="3">
            </div>
            
            <div class="form-group">
                <label for="cocineros_inasistentes">Cocineras inasistentes</label>
                <input type="number" id="cocineros_inasistentes" name="cocineros_inasistentes" value="0">
            </div>
            
            <div class="form-group">
                <label for="matricula_inscrita">Matrícula inscrita</label>
                <input type="number" id="matricula_inscrita" name="matricula_inscrita" value="100">
            </div>
            
            <div class="form-group">
                <label for="matricula_asistente">Matrícula asistente</label>
                <input type="number" id="matricula_asistente" name="matricula_asistente" value="95">
            </div>
            
            <div class="form-group">
                <label for="estudiantes_ingesta">Estudiantes que recibieron ingesta</label>
                <input type="number" id="estudiantes_ingesta" name="estudiantes_ingesta" value="95">
            </div>
            
            <div class="form-group">
                <label for="estudiantes_repiten">Estudiantes que repiten</label>
                <input type="number" id="estudiantes_repiten" name="estudiantes_repiten" value="5">
            </div>
            
            <div class="form-group">
                <label for="docentes_reciben">Docentes que reciben</label>
                <input type="number" id="docentes_reciben" name="docentes_reciben" value="10">
            </div>
            
            <div class="form-group">
                <label for="casos_vulnerables">Casos vulnerables</label>
                <input type="number" id="casos_vulnerables" name="casos_vulnerables" value="2">
            </div>
            
            <div class="form-group">
                <label for="total_comensales">Total comensales</label>
                <input type="number" id="total_comensales" name="total_comensales" value="112">
            </div>
            
            <div class="form-group">
                <label for="incidencias">Incidencias</label>
                <select id="incidencias" name="incidencias">
                    <option value="ninguna" selected>Ninguna</option>
                    <option value="falta_insumos">Falta de insumos</option>
                    <option value="falla_equipos">Falla de equipos</option>
                    <option value="otros">Otros</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="observaciones">Observaciones</label>
                <textarea id="observaciones" name="observaciones" rows="3">Todo funcionó correctamente en la prueba.</textarea>
            </div>
            
            <!-- Campos ocultos para las fechas -->
            <input type="hidden" name="fecha_inicio" value="2024-01-01">
            <input type="hidden" name="fecha_fin" value="2024-01-31">
            
            <div style="margin-top: 20px;">
                <button type="submit">Generar PDF de Prueba</button>
            </div>
        </form>
        
        <div style="margin-top: 30px; padding: 20px; background: #f5f5f5; border-radius: 4px;">
            <h3>Instrucciones:</h3>
            <ol>
                <li>Los campos marcados con * son requeridos</li>
                <li>El formulario ya tiene datos de prueba precargados</li>
                <li>Haz clic en "Generar PDF de Prueba" para probar</li>
                <li>Si funciona, el problema estaba en la validación JavaScript</li>
            </ol>
        </div>
        
        <div style="margin-top: 20px;">
            <a href="views/reportes.php">← Volver a Reportes</a>
        </div>
    </div>
</body>
</html>
