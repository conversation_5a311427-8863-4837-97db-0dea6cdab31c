.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}
.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 20px;
    top: -15%;
    border-radius: 5px;
    width: 95%;
    max-width: 900px;
    box-shadow: 0 0 10px rgba(0,0,0,0.3);
}
.edit-limits {
    margin-left: 0.5rem;
    padding: 0.25rem 0.5rem;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
.edit-limits:hover {
    background-color: #357abd;
}

/* Styles for modal registrar platos servidos y devueltos */
#platosModal.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.6);
}
#platosModal .modal-content {
    background-color: #fff;
    margin: 8% auto;
    padding: 20px 30px;
    border-radius: 8px;
    width: 90%;
    max-width: 850px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    animation: fadeInModal 0.3s ease-in-out;
}
#platosModal .modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    color: #202123;
}

#platosModal .modal-body {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 15px;
}
#platosModal .modal-footer .btn-primary {
    background-color: #10a37f;
    border: none;
    color: white;
}
#platosModal .modal-footer .btn-primary:hover {
    background-color: #10a37f;
}
@keyframes fadeInModal {
    from {opacity: 0; transform: translateY(-20px);}
    to {opacity: 1; transform: translateY(0);}
}

.filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}