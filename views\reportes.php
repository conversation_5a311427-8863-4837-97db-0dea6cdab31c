<?php
require_once __DIR__ . '/../controllers/AuthController.php';
require_once __DIR__ . '/../controllers/ReporteController.php';
require_once __DIR__ . '/../controllers/GenerarPDFController.php';

$auth = new AuthController();
$reporteController = new ReporteController();

if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$fecha_inicio = isset($_GET['fecha_inicio']) ? $_GET['fecha_inicio'] : date('Y-m-01');
$fecha_fin = isset($_GET['fecha_fin']) ? $_GET['fecha_fin'] : date('Y-m-t');

$hoy = date('Y-m-d');
// Clamp fecha_inicio and fecha_fin to today if they exceed today
if ($fecha_inicio > $hoy) {
    $fecha_inicio = $hoy;
}
if ($fecha_fin > $hoy) {
    $fecha_fin = $hoy;
}

// Obtener datos para los reportes
$resumenAsistencia = $reporteController->getResumenAsistencia($fecha_inicio, $fecha_fin);
$asistenciaPorDia = $reporteController->getAsistenciaPorDia($fecha_inicio, $fecha_fin);
$consumoInsumos = $reporteController->getConsumoInsumos($fecha_inicio, $fecha_fin);
$menusPopulares = $reporteController->getMenusPopulares($fecha_inicio, $fecha_fin);
$averageAttendancePerMenu = $reporteController->getAverageAttendancePerMenu($fecha_inicio, $fecha_fin);
$estadisticasGenerales = $reporteController->getEstadisticasGenerales($fecha_inicio, $fecha_fin);

// New data for form automation
$usuariosCarga = $reporteController->getUsuariosQueRealizaronGramaje($fecha_inicio, $fecha_fin);
$cantidadCocineras = $reporteController->getCantidadCocineras();
$matriculaTotal = $reporteController->getMatriculaTotal();
$totalAsistencia = $reporteController->getTotalAsistencia($fecha_inicio, $fecha_fin);
$totalPlatosServidos = $reporteController->getTotalPlatosServidos($fecha_inicio, $fecha_fin);
$platosServidosDocentes = $reporteController->getPlatosServidosDocentes($fecha_inicio, $fecha_fin);
$casosVulnerables = $reporteController->getCasosVulnerables();
$menuDelDia = $reporteController->getMenuDelDia($fecha_inicio, $fecha_fin);

// Calculate estudiantes que repiten (excedentes platos servidos sobre asistencia)
$estudiantesRepiten = 0;
if ($totalPlatosServidos > $totalAsistencia) {
    $estudiantesRepiten = $totalPlatosServidos - $totalAsistencia;
}

// Total comensales = estudiantes + docentes + otros
$totalComensales = ($resumenAsistencia['total_estudiantes'] ?? 0) + ($resumenAsistencia['total_docentes'] ?? 0) + $casosVulnerables;

// Preparar datos para gráficos
$datosAsistencia = [];
$datosConsumo = [];

while ($row = $asistenciaPorDia->fetch(PDO::FETCH_ASSOC)) {
    $datosAsistencia[] = [
        'fecha' => $row['fecha'],
        'total' => $row['total_asistencia'],
        'tipo' => $row['tipo']
    ];
}

while ($row = $consumoInsumos->fetch(PDO::FETCH_ASSOC)) {
    $datosConsumo[] = [
        'producto' => $row['producto'],
        'total' => $row['total_consumido'],
        'unidad' => $row['unidad']
    ];
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reportes - Sistema de Comedor Escolar</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/reportes.css">
</head>
<body>
    <?php include 'partials/sidebar.php'; ?>
    <?php include 'partials/navigation_buttons.php'; ?>

    <main class="main-content">
        <div class="report-container">
            <div class="report-header">
                <h2>Reportes del Sistema</h2>
                <form method="GET" class="mb-4" id="filterForm">
                    <div class="form-grid" style="align-items: flex-end;">
                        <div class="form-group">
                            <label for="fecha_inicio">Fecha Inicio:</label>
                            <input type="date" id="fecha_inicio" name="fecha_inicio" 
                                   value="<?php echo $fecha_inicio; ?>" max="<?php echo $hoy; ?>" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="fecha_fin">Fecha Fin:</label>
                            <input type="date" id="fecha_fin" name="fecha_fin" 
                                   value="<?php echo $fecha_fin; ?>" max="<?php echo $hoy; ?>" class="form-control">
                        </div>
                        <div class="form-group" style="margin-bottom: 0.7;">
                            <button type="submit" class="btn btn-primary" style="margin-right: 10px;">Filtrar</button>
                            <button type="button" id="reporteDiarioBtn" class="btn btn-secondary">Reporte Diario</button>
                        </div>
                    </div>
                </form>
                <script>
                    document.getElementById('reporteDiarioBtn').addEventListener('click', function() {
                        const hoy = new Date().toISOString().split('T')[0];
                        document.getElementById('fecha_inicio').value = hoy;
                        document.getElementById('fecha_fin').value = hoy;
                        document.getElementById('filterForm').submit();
                    });
                </script>
            </div>

            <div class="report-nav">
                <button class="tab-btn active" data-tab="resumen">Resumen General</button>
                <button class="tab-btn" data-tab="asistencia">Asistencia</button>
                <button class="tab-btn" data-tab="consumo">Consumo</button>
                <button class="tab-btn" data-tab="menus">Menús</button>
                <button class="tab-btn" data-tab="formulario">Formulario</button>
            </div>

            <!-- Resumen General -->
            <div id="resumen" class="report-section active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Estudiantes</h3>
                        <div class="stat-value"><?php echo $resumenAsistencia['total_estudiantes']; ?></div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Docentes</h3>
                        <div class="stat-value"><?php echo $resumenAsistencia['total_docentes']; ?></div>
                    </div>
                    <div class="stat-card">
                        <h3>Promedio Diario</h3>
                        <div class="stat-value"><?php echo round($resumenAsistencia['promedio_diario']); ?></div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="resumenChart"></canvas>
                </div>
            </div>

            <!-- Asistencia -->
            <div id="asistencia" class="report-section">
                <div class="chart-container">
                    <canvas id="asistenciaChart"></canvas>
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Días Registrados</h3>
                        <div class="stat-value"><?php echo $resumenAsistencia['total_dias']; ?></div>
                    </div>
                    <div class="stat-card">
                        <h3>Promedio Estudiantes</h3>
                        <div class="stat-value"><?php echo round($resumenAsistencia['total_estudiantes'] / $resumenAsistencia['total_dias']); ?></div>
                    </div>
                    <div class="stat-card">
                        <h3>Promedio Docentes</h3>
                        <div class="stat-value"><?php echo round($resumenAsistencia['total_docentes'] / $resumenAsistencia['total_dias']); ?></div>
                    </div>
                </div>
            </div>

            <!-- Consumo -->
            <div id="consumo" class="report-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Productos</h3>
                        <div class="stat-value"><?php echo $estadisticasGenerales['total_productos']; ?></div>
                    </div>
                    <div class="stat-card">
                        <h3>Productos Bajo Stock</h3>
                        <div class="stat-value"><?php echo $estadisticasGenerales['productos_bajo_stock']; ?></div>
                    </div>
                    <div class="stat-card">
                        <h3>Consumo Total</h3>
                        <div class="stat-value"><?php echo round($estadisticasGenerales['consumo_total'], 2); ?> kg</div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="consumoChart"></canvas>
                </div>
            </div>

            <!-- Menús -->
            <div id="menus" class="report-section">
                <div class="chart-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Menú</th>
                                <th>Veces Utilizado</th>
                                <th>Última Fecha</th>
                                <th>Promedio Asistencia</th>
                            </tr>
                        </thead>
                            <tbody>
                                <?php while ($menu = $menusPopulares->fetch(PDO::FETCH_ASSOC)): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($menu['menu']); ?></td>
                                        <td><?php echo $menu['veces_utilizado']; ?></td>
                                        <td><?php echo $menu['ultima_fecha']; ?></td>
                                        <td><?php 
                                            $promedio = isset($averageAttendancePerMenu[$menu['id_menu']]) ? $averageAttendancePerMenu[$menu['id_menu']] : 0;
                                            echo round($promedio);
                                        ?></td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                    </table>
                </div>
            </div>

            <!-- Formulario -->
            <div id="formulario" class="report-section">
                <div class="form-section">
                    <h3>Formulario de Reporte</h3>
                    <form id="reportForm" method="post" action="../api/generar_pdf.php" class="needs-validation" target="_blank" novalidate>
                        <!-- Campos del formulario -->
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="parroquia">Parroquia</label>
                                <input type="text" class="form-control" id="parroquia" name="parroquia" required>
                            </div>
                            <div class="form-group">
                                <label for="responsable">Responsable</label>
                                <input type="text" class="form-control" id="responsable" name="responsable" required>
                            </div>
                            <div class="form-group">
                                <label for="institucion">Institución</label>
                                <input type="text" class="form-control" id="institucion" name="institucion" required>
                            </div>
                            <div class="form-group">
                                <label for="codigo_dea">Código DEA</label>
                                <input type="text" class="form-control" id="codigo_dea" name="codigo_dea" required>
                            </div>
                            <div class="form-group">
                                <label for="nivel">Nivel</label>
                                <select class="form-control" id="nivel" name="nivel" required>
                                    <option value="">Seleccionar...</option>
                                    <option value="Inicial">Inicial</option>
                                    <option value="Primaria">Primaria</option>
                                    <option value="Secundaria">Secundaria</option>
                                    <option value="Mixto">Mixto</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="menu_dia">Menú del día</label>
                                <input type="text" class="form-control" id="menu_dia" name="menu_dia" value="<?php echo htmlspecialchars($menuDelDia); ?>" readonly>
                            </div>
                            <div class="form-group">
                                <label for="cant_cocineros">Cantidad de cocineras</label>
                                <input type="number" class="form-control" id="cant_cocineros" name="cant_cocineros" value="<?php echo $cantidadCocineras; ?>" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="cocineros_inasistentes">Cocineras inasistentes</label>
                                <input type="number" class="form-control" id="cocineros_inasistentes" name="cocineros_inasistentes" value="0" min="0" max="<?php echo $cantidadCocineras; ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="matricula_inscrita">Matrícula inscrita</label>
                                <input type="number" class="form-control" id="matricula_inscrita" name="matricula_inscrita" value="<?php echo $matriculaTotal; ?>" readonly>
                            </div>
                            <div class="form-group">
                                <label for="matricula_asistente">Matrícula asistente</label>
                                <input type="number" class="form-control" id="matricula_asistente" name="matricula_asistente" value="<?php echo $totalAsistencia; ?>" readonly>
                            </div>
                            <div class="form-group">
                                <label for="estudiantes_ingesta">Estudiantes que recibieron ingesta</label>
                                <input type="number" class="form-control" id="estudiantes_ingesta" name="estudiantes_ingesta" value="<?php echo $totalPlatosServidos; ?>" readonly>
                            </div>
                            <div class="form-group">
                                <label for="estudiantes_repiten">Estudiantes que repiten</label>
                                <input type="number" class="form-control" id="estudiantes_repiten" name="estudiantes_repiten" value="<?php echo $estudiantesRepiten; ?>" min="0">
                            </div>
                            <div class="form-group">
                                <label for="docentes_reciben">Docentes que reciben</label>
                                <input type="number" class="form-control" id="docentes_reciben" name="docentes_reciben" value="<?php echo $platosServidosDocentes; ?>" min="0">
                            </div>
                            <div class="form-group">
                                <label for="casos_vulnerables">Casos vulnerables</label>
                                <input type="number" class="form-control" id="casos_vulnerables" name="casos_vulnerables" value="<?php echo $casosVulnerables; ?>" min="0">
                            </div>
                            <div class="form-group">
                                <label for="total_comensales">Total comensales</label>
                                <input type="number" class="form-control" id="total_comensales" name="total_comensales" value="<?php echo $totalComensales; ?>" readonly>
                            </div>
                            <div class="form-group">
                                <label for="incidencias">Incidencias</label>
                                <select class="form-control" id="incidencias" name="incidencias" required>
                                    <option value="">Seleccionar...</option>
                                    <option value="ninguna">Ninguna</option>
                                    <option value="falta_insumos">Falta de insumos</option>
                                    <option value="falla_equipos">Falla de equipos</option>
                                    <option value="otros">Otros</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="observaciones">Observaciones</label>
                                <textarea class="form-control" id="observaciones" name="observaciones" rows="3"></textarea>
                            </div>
                        </div>
                        
                        <!-- Campos ocultos para las fechas -->
                        <input type="hidden" name="fecha_inicio" value="<?php echo $fecha_inicio; ?>">
                        <input type="hidden" name="fecha_fin" value="<?php echo $fecha_fin; ?>">
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Generar PDF</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Botones de exportación -->
            <div class="export-buttons">
                <a href="../generar_pdf.php" class="btn btn-primary" id="exportPdfBtn" target="_blank">Exportar a PDF</a>
                <button class="btn btn-success" id="exportExcelBtn">Exportar a Excel</button>
            </div>

            <!-- Formulario para generar PDF -->
            <form id="reportForm" action="../api/generar_pdf.php" method="post" target="_blank">
                <!-- Campos del formulario -->
                <div class="form-grid">
                    <div class="form-group">
                        <label for="fecha_inicio">Fecha Inicio</label>
                        <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" value="<?php echo $fecha_inicio; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="fecha_fin">Fecha Fin</label>
                        <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" value="<?php echo $fecha_fin; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="parroquia">Parroquia</label>
                        <input type="text" class="form-control" id="parroquia" name="parroquia" required>
                    </div>
                    <div class="form-group">
                        <label for="responsable">Responsable</label>
                        <input type="text" class="form-control" id="responsable" name="responsable" required>
                    </div>
                    <div class="form-group">
                        <label for="institucion">Institución</label>
                        <input type="text" class="form-control" id="institucion" name="institucion" required>
                    </div>
                    <div class="form-group">
                        <label for="cant_cocineros">Cantidad de Cocineras</label>
                        <input type="number" class="form-control" id="cant_cocineros" name="cant_cocineros" value="<?php echo $cantidadCocineras; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="cocineros_inasistentes">Cocineras Inasistentes</label>
                        <input type="number" class="form-control" id="cocineros_inasistentes" name="cocineros_inasistentes" value="0" required>
                    </div>
                    <div class="form-group">
                        <label for="matricula_inscrita">Matrícula Inscrita</label>
                        <input type="number" class="form-control" id="matricula_inscrita" name="matricula_inscrita" value="<?php echo $matriculaTotal; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="matricula_asistente">Matrícula Asistente</label>
                        <input type="number" class="form-control" id="matricula_asistente" name="matricula_asistente" value="<?php echo $totalAsistencia; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="estudiantes_ingesta">Estudiantes que recibieron la ingesta</label>
                        <input type="number" class="form-control" id="estudiantes_ingesta" name="estudiantes_ingesta" value="<?php echo $totalPlatosServidos; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="docentes_reciben">Docentes que recibieron la ingesta</label>
                        <input type="number" class="form-control" id="docentes_reciben" name="docentes_reciben" value="<?php echo $platosServidosDocentes; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="casos_vulnerables">Casos Vulnerables</label>
                        <input type="number" class="form-control" id="casos_vulnerables" name="casos_vulnerables" value="<?php echo $casosVulnerables; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="total_comensales">Total de Comensales</label>
                        <input type="number" class="form-control" id="total_comensales" name="total_comensales" value="<?php echo $totalPlatosServidos + $platosServidosDocentes; ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="incidencias">Incidencias</label>
                        <textarea class="form-control" id="incidencias" name="incidencias" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="observaciones">Observaciones</label>
                        <textarea class="form-control" id="observaciones" name="observaciones" rows="3"></textarea>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Generar PDF</button>
                </div>
            </form>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script>
        window.datosAsistencia = <?php echo json_encode($datosAsistencia); ?>;
        window.datosConsumo = <?php echo json_encode($datosConsumo); ?>;
    </script>
    <!-- Custom modal for unsaved changes alert -->
    <div id="unsavedChangesModal" class="modal">
        <div class="modal-content warning-modal-content">
            <div class="modal-icon warning-icon">⚠️</div>
            <h2 class="warning-title">Advertencia de Seguridad</h2>
            <p class="warning-message">Hay cambios sin guardar. ¿Seguro que quieres salir?</p>
            <div class="warning-buttons">
                <button id="confirmExitBtn" class="btn btn-primary">Confirmar</button>
                <button id="cancelExitBtn" class="btn btn-secondary">Cancelar</button>
            </div>
        </div>
    </div>
    <script src="../assets/js/reportes.js"></script>
</body>
</html>



