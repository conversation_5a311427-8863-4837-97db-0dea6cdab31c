<?php
require_once __DIR__ . '/controllers/AuthController.php';

$auth = new AuthController();
$usuarioLogueado = $auth->getCurrentUserName();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Seguridad - Campo Responsable</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #10a37f; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0d8f6b; }
        .container { max-width: 600px; margin: 0 auto; }
        .readonly-field {
            background-color: #f8f9fa !important;
            cursor: not-allowed !important;
            border-color: #dee2e6 !important;
        }
        .readonly-field:focus {
            box-shadow: none !important;
            border-color: #dee2e6 !important;
        }
        .security-note {
            font-size: 0.875rem;
            color: #6c757d;
            font-style: italic;
        }
        .test-box {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #10a37f;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Prueba de Seguridad - Campo Responsable</h1>
        
        <div class="test-box">
            <h3>🔒 Medidas de Seguridad Implementadas</h3>
            <ul>
                <li><strong>Campo readonly:</strong> No se puede editar desde la interfaz</li>
                <li><strong>Protección JavaScript:</strong> Previene modificaciones por teclado, paste, drag & drop</li>
                <li><strong>Validación del servidor:</strong> El servidor siempre usa el usuario logueado</li>
                <li><strong>Estilo visual:</strong> Indica claramente que el campo no es editable</li>
            </ul>
        </div>
        
        <form method="post" action="api/generar_pdf.php" target="_blank">
            <div class="form-group">
                <label for="parroquia">Parroquia *</label>
                <input type="text" id="parroquia" name="parroquia" value="Parroquia de Prueba" required>
            </div>
            
            <div class="form-group">
                <label for="responsable">Responsable</label>
                <input type="text" class="readonly-field" id="responsable" name="responsable" value="<?php echo htmlspecialchars($usuarioLogueado ?: 'Usuario de Prueba'); ?>" readonly required>
                <small class="security-note">🔒 Campo protegido - Se asigna automáticamente al usuario logueado</small>
            </div>
            
            <div class="form-group">
                <label for="institucion">Institución *</label>
                <input type="text" id="institucion" name="institucion" value="Escuela de Prueba" required>
            </div>
            
            <!-- Campos ocultos para completar el formulario -->
            <input type="hidden" name="codigo_dea" value="12345">
            <input type="hidden" name="nivel" value="Primaria">
            <input type="hidden" name="menu_dia" value="Arroz con pollo">
            <input type="hidden" name="cant_cocineros" value="3">
            <input type="hidden" name="cocineros_inasistentes" value="0">
            <input type="hidden" name="matricula_inscrita" value="100">
            <input type="hidden" name="matricula_asistente" value="95">
            <input type="hidden" name="estudiantes_ingesta" value="95">
            <input type="hidden" name="estudiantes_repiten" value="5">
            <input type="hidden" name="docentes_reciben" value="10">
            <input type="hidden" name="casos_vulnerables" value="2">
            <input type="hidden" name="total_comensales" value="112">
            <input type="hidden" name="incidencias" value="ninguna">
            <input type="hidden" name="observaciones" value="Prueba de seguridad del campo responsable">
            <input type="hidden" name="fecha_inicio" value="2024-01-01">
            <input type="hidden" name="fecha_fin" value="2024-01-31">
            
            <div style="margin-top: 20px;">
                <button type="submit">Generar PDF de Prueba</button>
                <button type="button" onclick="testSecurity()">Probar Seguridad</button>
            </div>
        </form>
        
        <div class="test-box warning">
            <h3>⚠️ Pruebas de Seguridad</h3>
            <p>Intenta hacer lo siguiente en el campo "Responsable":</p>
            <ol>
                <li>Hacer clic en el campo</li>
                <li>Intentar escribir algo</li>
                <li>Intentar copiar y pegar texto</li>
                <li>Intentar seleccionar el texto</li>
            </ol>
            <p><strong>Resultado esperado:</strong> Ninguna de estas acciones debería funcionar.</p>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="views/reportes.php">← Volver a Reportes</a> |
            <a href="test_usuario.php">Ver Info de Usuario</a>
        </div>
    </div>

    <script>
        // Proteger el campo responsable
        function protegerCampoResponsable() {
            const responsableField = document.getElementById('responsable');
            if (responsableField) {
                // Prevenir modificaciones por teclado
                responsableField.addEventListener('keydown', function(e) {
                    e.preventDefault();
                    return false;
                });
                
                // Prevenir modificaciones por paste
                responsableField.addEventListener('paste', function(e) {
                    e.preventDefault();
                    return false;
                });
                
                // Prevenir modificaciones por drag & drop
                responsableField.addEventListener('drop', function(e) {
                    e.preventDefault();
                    return false;
                });
                
                // Prevenir selección de texto
                responsableField.addEventListener('selectstart', function(e) {
                    e.preventDefault();
                    return false;
                });
                
                // Mostrar mensaje si alguien intenta modificar
                responsableField.addEventListener('focus', function() {
                    console.log('Campo responsable protegido - no se puede modificar');
                });
            }
        }

        function testSecurity() {
            alert('Intenta modificar el campo "Responsable" de cualquier manera. Debería estar completamente protegido.');
        }

        // Inicializar protección al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            protegerCampoResponsable();
        });
    </script>
</body>
</html>
