    FASE II – REQUERIMIENTOS, DISEÑO Y DESARROLLO DEL PROYECTO (SOFTWARE)

    I. REQUERIMIENTOS DEL SOFTWARE

    1. Lista de Requerimientos Funcionales:
    - Gestión de usuarios con roles diferenciados (administrador, usuario base, cocinero).
    - Control de asistencia por grado y sección.
    - Planificación y gestión de menús diarios.
    - Cálculo automático de gramaje para los menús.
    - Gestión de inventario de productos y control de stock.
    - Registro de ingresos y consumos de insumos.
    - Generación de reportes estadísticos y resúmenes.
    - Interfaz moderna, responsiva y segura.
    - Autenticación y control de acceso basado en roles.
    - Registro y actualización de datos maestros y transacciones.

    2. Especificación de Requerimientos Funcionales (Plantilla):

    | ID  | Requerimiento                         | Descripción                                                                                  |
    |------|-------------------------------------|----------------------------------------------------------------------------------------------|
    | RF1  | Gestión de Usuarios                  | Permite crear, modificar, listar y desactivar usuarios con roles específicos.                |
    | RF2  | Control de Asistencia               | Registrar asistencia diaria por grado y sección, diferenciando por género.                   |
    | RF3  | Planificación de Menús              | Crear, modificar, consultar y eliminar menús diarios con observaciones.                      |
    | RF4  | Cálculo de Gramaje                  | Calcular automáticamente el gramaje necesario para cada plato según el menú.                 |
    | RF5  | Gestión de Inventario               | Controlar stock de productos, registrar ingresos y consumos, alertar productos bajo stock.   |
    | RF6  | Generación de Reportes              | Generar reportes de asistencia, consumo, menús populares y estadísticas generales.           |
    | RF7  | Seguridad                         | Autenticación, roles, validación de formularios y protección contra ataques comunes.         |

    3. Diagrama de Caso de Uso del Software:
    - Usuarios (Administrador, Cocinero, Usuario Base) interactúan con el sistema para gestionar usuarios, registrar asistencia, planificar menús, gestionar inventario y generar reportes.
    - Casos de uso principales: Gestionar Usuarios, Registrar Asistencia, Planificar Menús, Controlar Inventario, Generar Reportes.

    4. Especificaciones de los Casos de Uso del Software (Maestros y Transacciones):

    | Caso de Uso          | Descripción                                                                 |
    |----------------------|-----------------------------------------------------------------------------|
    | Gestionar Usuarios   | Crear, modificar, listar y desactivar usuarios con roles.                   |
    | Registrar Asistencia | Registrar asistencia diaria por grado y sección.                           |
    | Planificar Menús     | Crear y modificar menús diarios con detalles y observaciones.              |
    | Controlar Inventario | Registrar ingresos y consumos, actualizar stock, alertar bajo stock.       |
    | Generar Reportes     | Obtener estadísticas y reportes de asistencia, consumo y menús.            |

    5. Especificación de Requerimientos No Funcionales:

    | ID  | Requerimiento No Funcional          | Descripción                                                                                  |
    |------|------------------------------------|----------------------------------------------------------------------------------------------|
    | RNF1 | Rendimiento                       | El sistema debe responder en menos de 3 segundos para operaciones comunes.                   |
    | RNF2 | Seguridad                        | Implementar autenticación segura, roles y protección contra SQL Injection y XSS.             |
    | RNF3 | Usabilidad                      | Interfaz intuitiva, moderna y responsiva para diferentes dispositivos.                        |
    | RNF4 | Mantenibilidad                  | Código modular y documentado para facilitar mantenimiento y escalabilidad.                   |
    | RNF5 | Compatibilidad                  | Compatible con navegadores modernos y PHP 7.4+, MySQL 5.7+.                                 |

    II. DESARROLLO DEL SOFTWARE (Texto descriptivo)

    1. Nombre del Software:
    Sistema de Gestión de Comedor Escolar

    2. Objetivos del Software:
    - Objetivo General:
    Facilitar la gestión integral del comedor escolar mediante un sistema web que permita controlar usuarios, asistencia, menús, inventario y reportes.
    - Objetivos Específicos:
    - Gestionar usuarios con roles diferenciados.
    - Registrar y controlar la asistencia diaria de estudiantes y personal.
    - Planificar menús diarios y calcular gramajes automáticamente.
    - Controlar el inventario de insumos y productos.
    - Generar reportes estadísticos para la toma de decisiones.

    3. Alcance del Software:
    El sistema abarca la gestión completa del comedor escolar, desde la administración de usuarios hasta la generación de reportes, incluyendo control de asistencia, planificación de menús, cálculo de gramajes, gestión de inventario y reportes.

    4. Metodología utilizada:
    - Metodología de Desarrollo: Desarrollo basado en PHP con arquitectura MVC (Modelo-Vista-Controlador), uso de MySQL para base de datos y React para interfaz frontend.
    - Metodología de Calidad: Implementación de buenas prácticas de seguridad, validación de datos, control de acceso y pruebas funcionales.

    5. Arquitectura del software:
    Arquitectura cliente-servidor con backend en PHP (controladores, modelos, vistas), base de datos MySQL y frontend React para interfaz de usuario. Uso de MVC para separación de responsabilidades.

    6. Modelo de Diseño:
    - Diagrama de Clases: Entidades principales incluyen Usuario, Menu, Asistencia, Producto, Consumo, entre otras.
    - Diagrama de Objetos: Instancias de clases para gestionar operaciones específicas como registro de asistencia, gestión de menús y control de inventario.

    7. Modelo de Datos:
    - Modelo Entidad-Relación (E/R): Tablas principales incluyen usuarios, menu, asistencia, producto, consumo, matricula, entre otras.
    - Diccionario de Datos: Definición de campos y relaciones entre tablas para asegurar integridad y consistencia.

    8. Mapa de Navegación y Carta estructurada:
    - Navegación basada en roles con acceso a módulos de usuarios, asistencia, menús, inventario y reportes.
    - Vista de usuarios por servicios con formularios para cada módulo y panel de control centralizado.

    9. Interfaz de usuario (Formularios):
    - Formularios para gestión de usuarios, registro de asistencia, planificación de menús, control de inventario y generación de reportes.
    - Interfaz responsiva y moderna con validación de datos y control de acceso.

    10. Diagrama de componentes y Diagrama de despliegue:
    - Componentes principales: Frontend React, Backend PHP MVC, Base de datos MySQL.
    - Despliegue en servidor local con XAMPP, acceso vía navegador web.

    Reflexión Crítica de la Fase II:
    Durante esta fase se logró definir claramente los requerimientos funcionales y no funcionales, así como diseñar la arquitectura y modelos de datos que soportan el sistema. La implementación basada en MVC facilita la mantenibilidad y escalabilidad. Se identificaron áreas para mejora futura, como la inclusión de diagramas visuales y documentación más detallada de casos de uso. La integración de seguridad y usabilidad fue prioritaria para garantizar un sistema robusto y amigable para los usuarios finales.
