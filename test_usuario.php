<?php
require_once __DIR__ . '/controllers/AuthController.php';

$auth = new AuthController();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Usuario Logueado</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .info-box { background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .btn { background: #10a37f; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; }
        .btn:hover { background: #0d8f6b; }
    </style>
</head>
<body>
    <h1>Prueba de Usuario Logueado</h1>
    
    <?php if ($auth->isLoggedIn()): ?>
        <?php 
        $currentUser = $auth->getCurrentUser();
        $userName = $auth->getCurrentUserName();
        ?>
        
        <div class="info-box success">
            <h3>✅ Usuario Logueado Correctamente</h3>
            <p><strong>ID:</strong> <?php echo $currentUser['id']; ?></p>
            <p><strong>Username:</strong> <?php echo htmlspecialchars($currentUser['username']); ?></p>
            <p><strong>Nombre Completo:</strong> <?php echo htmlspecialchars($currentUser['nombre_completo']); ?></p>
            <p><strong>Rol:</strong> <?php echo htmlspecialchars($currentUser['rol']); ?></p>
            <p><strong>Nombre para Formulario:</strong> <?php echo htmlspecialchars($userName); ?></p>
        </div>
        
        <div class="info-box">
            <h3>Datos de Sesión</h3>
            <p><strong>user_id:</strong> <?php echo $_SESSION['user_id'] ?? 'No definido'; ?></p>
            <p><strong>rol:</strong> <?php echo $_SESSION['rol'] ?? 'No definido'; ?></p>
            <p><strong>user_name:</strong> <?php echo $_SESSION['user_name'] ?? 'No definido'; ?></p>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="views/reportes.php" class="btn">Ir a Reportes</a>
            <a href="test_form.php" class="btn">Probar Formulario</a>
            <a href="views/logout.php" class="btn" style="background: #dc3545;">Cerrar Sesión</a>
        </div>
        
    <?php else: ?>
        
        <div class="info-box error">
            <h3>❌ No hay usuario logueado</h3>
            <p>Necesitas iniciar sesión para ver esta información.</p>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="views/login.php" class="btn">Iniciar Sesión</a>
        </div>
        
    <?php endif; ?>
    
    <div style="margin-top: 40px; padding: 20px; background: #e9ecef; border-radius: 4px;">
        <h3>Información del Sistema</h3>
        <p><strong>Usuarios disponibles en la base de datos:</strong></p>
        <ul>
            <li><strong>yerson</strong> - Administrador (contraseña: 123456789y)</li>
            <li><strong>Cocinero</strong> - Cocinero</li>
            <li><strong>usuario</strong> - Usuario normal</li>
        </ul>
        <p><em>Si no estás logueado, usa cualquiera de estos usuarios para iniciar sesión.</em></p>
    </div>
</body>
</html>
