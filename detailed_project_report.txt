Informe detallado del proyecto "Sistema de Gestión de Comedor Escolar":

1. Descripción general del proyecto:
Este proyecto es un sistema web desarrollado en PHP y MySQL para la gestión integral de un comedor escolar. Permite administrar usuarios con diferentes roles, controlar la asistencia por grado y sección, planificar menús, calcular gramajes automáticamente, gestionar inventarios y generar reportes. La interfaz es moderna y responsiva.

2. Estructura de carpetas y archivos principales:
- assets/: Contiene archivos estáticos para el frontend, como CSS y JavaScript.
- config/: Archivos de configuración, incluyendo la conexión a la base de datos y scripts SQL para el esquema.
- controllers/: Controladores PHP que manejan la lógica de negocio y las solicitudes HTTP.
- models/: Modelos PHP que representan las entidades de la base de datos y contienen métodos para CRUD.
- supabase/migrations/: Scripts SQL para migraciones de la base de datos.
- views/: Vistas PHP que generan la interfaz de usuario, organizadas en subcarpetas por módulos.
- src/: Contiene archivos TypeScript/React, posiblemente para una interfaz SPA o panel administrativo.
- Archivos raíz como index.php, README.md, package.json, etc.

3. Explicación de controladores clave:

- UserController.php:
  Gestiona usuarios. Funciones principales:
  - getAllUsers(): Obtiene todos los usuarios.
  - createUser($username, $password, $nombre_completo, $rol): Crea un nuevo usuario.
  - updateUser($id, $nombre_completo, $rol, $estado): Actualiza datos de usuario.

- AuthController.php:
  Maneja autenticación y sesiones.
  - login($username, $password): Verifica credenciales, soporta actualización de contraseña en texto plano a hash.
  - logout(): Cierra sesión.
  - isLoggedIn(): Verifica si hay sesión activa.
  - checkRole($allowed_roles): Verifica roles permitidos.

- MenuController.php:
  Administra menús.
  - getAllMenus(): Obtiene todos los menús activos.
  - getMenuByDate($fecha): Obtiene menú por fecha.
  - createMenu($nombre, $observacion, $fecha, $created_by): Crea un menú.
  - updateMenu($id_menu, $nombre, $observacion, $fecha): Actualiza menú.
  - deleteMenu($id_menu): Elimina (soft delete) menú.

- AsistenciaController.php:
  Controla la asistencia.
  - getMatriculas($tipo, $grado): Obtiene matrículas por tipo y grado.
  - registrarAsistencia($fecha, $asistencias): Registra asistencia diaria.
  - getAsistenciasPorFecha($fecha): Obtiene asistencias por fecha.
  - actualizarMatricula($id_matricula, $grado, $seccion): Actualiza matrícula.
  - registrarPlatos(...): Registra platos servidos y devueltos.

- ReporteController.php:
  Genera reportes estadísticos.
  - getAverageAttendancePerMenu($fecha_inicio, $fecha_fin): Promedio de asistencia por menú.
  - getResumenAsistencia($fecha_inicio, $fecha_fin): Resumen de asistencia total.
  - getConsumoInsumos($fecha_inicio, $fecha_fin): Consumo total de insumos.
  - getMenusPopulares($fecha_inicio, $fecha_fin): Menús más usados.
  - getEstadisticasGenerales($fecha_inicio, $fecha_fin): Estadísticas generales del sistema.

4. Explicación de modelos clave:

- Usuario.php:
  Representa la tabla usuarios.
  - login(): Obtiene usuario para autenticación.
  - create(): Crea usuario con contraseña hasheada.
  - read(): Obtiene todos los usuarios.
  - update(): Actualiza datos de usuario.
  - updatePassword(): Actualiza contraseña.

- Menu.php:
  Representa la tabla menu.
  - create(), read(), readByDate(), readById(), update(), delete(): CRUD para menús.

- Asistencia.php:
  Representa la tabla asistencia.
  - create(), readByDate(), update(), readByDateAndMatricula(): CRUD para asistencia.

5. Ejemplo de código (UserController.php - createUser):

```php
public function createUser($username, $password, $nombre_completo, $rol) {
    $this->usuario->username = $username;
    $this->usuario->password = $password;
    $this->usuario->nombre_completo = $nombre_completo;
    $this->usuario->rol = $rol;
    
    return $this->usuario->create();
}
```

6. Consideraciones adicionales:
- Seguridad: autenticación con sesiones, contraseñas hasheadas, validación y control de acceso por roles.
- Uso de migraciones SQL para gestión de base de datos.
- Interfaz moderna con Tailwind CSS y JavaScript para funcionalidades frontend.

Este informe proporciona una visión completa para entender la estructura, funcionalidades y código base del proyecto.
