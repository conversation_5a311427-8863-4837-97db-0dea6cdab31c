<?php
session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/Usuario.php';

class AuthController {
    private $db;
    private $usuario;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->usuario = new Usuario($this->db);
    }

    public function login($username, $password) {
        $this->usuario->username = $username;

        // Obtener datos completos del usuario
        $query = "SELECT id, password, rol, estado, nombre_completo, username FROM usuarios WHERE username = ? LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(1, $username);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            // Si la contraseña está en texto plano (primera vez)
            if ($row['password'] === 'yerson123') {
                // Actualizar a hash
                $hashedPassword = password_hash('123456789y', PASSWORD_DEFAULT);
                $this->usuario->id = $row['id'];
                $this->usuario->password = $hashedPassword;
                $this->usuario->updatePassword();

                // Verificar con la nueva contraseña
                if ($password === '123456789y') {
                    $_SESSION['user_id'] = $row['id'];
                    $_SESSION['rol'] = $row['rol'];
                    $_SESSION['user_name'] = !empty($row['nombre_completo']) ? $row['nombre_completo'] : $row['username'];
                    return true;
                }
            } else {
                // Verificar con password_verify
                if (password_verify($password, $row['password']) && $row['estado']) {
                    $_SESSION['user_id'] = $row['id'];
                    $_SESSION['rol'] = $row['rol'];
                    $_SESSION['user_name'] = !empty($row['nombre_completo']) ? $row['nombre_completo'] : $row['username'];
                    return true;
                }
            }
        }
        return false;
    }

    public function logout() {
        session_destroy();
        return true;
    }

    public function isLoggedIn() {
        return isset($_SESSION['user_id']);
    }

    public function checkRole($allowed_roles) {
        if(!$this->isLoggedIn()) return false;
        return in_array($_SESSION['rol'], $allowed_roles);
    }

    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }

        $query = "SELECT id, username, nombre_completo, rol FROM usuarios WHERE id = ? LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(1, $_SESSION['user_id']);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getCurrentUserName() {
        if (!$this->isLoggedIn()) {
            return '';
        }

        // Si ya está en la sesión, usarlo
        if (isset($_SESSION['user_name'])) {
            return $_SESSION['user_name'];
        }

        // Si no está en la sesión, obtenerlo de la base de datos
        $user = $this->getCurrentUser();
        if ($user) {
            $name = !empty($user['nombre_completo']) ? $user['nombre_completo'] : $user['username'];
            $_SESSION['user_name'] = $name; // Guardarlo para próximas veces
            return $name;
        }

        return '';
    }
}